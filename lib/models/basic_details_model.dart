class BasicDetailsModel {
  final String alcoholConsumption;
  final String bloodGroup;
  final String clinic;
  final String created;
  final String dateOfBirth;
  final String diabetesMedication;
  final List<String> diagnosis;
  final String email;
  final String ethnicity;
  final String fallsLastYear;
  final String feet;
  final String firstName;
  final String gender;
  final String hearingAbility;
  final String hypertensionMedication;
  final String inches;
  final String lastName;
  final String lastUpdated;
  final String lbsWeight;
  final String maritalStatus;
  final String otherConditions;
  final String patientId;
  final String phoneNumber;
  final String program;
  final String providerName;
  final String recreationalDrugUse;
  final String smoking;
  final String smokingSubstance;
  final String thyroidMedication;

  BasicDetailsModel({
    required this.alcoholConsumption,
    required this.bloodGroup,
    required this.clinic,
    required this.created,
    required this.dateOfBirth,
    required this.diabetesMedication,
    required this.diagnosis,
    required this.email,
    required this.ethnicity,
    required this.fallsLastYear,
    required this.feet,
    required this.firstName,
    required this.gender,
    required this.hearingAbility,
    required this.hypertensionMedication,
    required this.inches,
    required this.lastName,
    required this.lastUpdated,
    required this.lbsWeight,
    required this.maritalStatus,
    required this.otherConditions,
    required this.patientId,
    required this.phoneNumber,
    required this.program,
    required this.providerName,
    required this.recreationalDrugUse,
    required this.smoking,
    required this.smokingSubstance,
    required this.thyroidMedication,
  });

  factory BasicDetailsModel.fromJson(Map<String, dynamic> json) {
    List<String> diagnosisList = [];
    if (json['diagnosis'] != null) {
      if (json['diagnosis'] is List) {
        diagnosisList = List<String>.from(json['diagnosis']);
      } else if (json['diagnosis'] is String) {
        // For backward compatibility with existing data
        diagnosisList = [json['diagnosis']];
      }
    }

    return BasicDetailsModel(
      alcoholConsumption: json['alcoholConsumption'] ?? '',
      bloodGroup: json['bloodGroup'] ?? '',
      clinic: json['clinic'] ?? '',
      created: json['created'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      diabetesMedication: json['diabetesMedication'] ?? '',
      diagnosis: diagnosisList,
      email: json['email'] ?? '',
      ethnicity: json['ethnicity'] ?? '',
      fallsLastYear: json['fallsLastYear'] ?? '',
      feet: json['feet'] ?? '',
      firstName: json['firstName'] ?? '',
      gender: json['gender'] ?? '',
      hearingAbility: json['hearingAbility'] ?? '',
      hypertensionMedication: json['hypertensionMedication'] ?? '',
      inches: json['inches'] ?? '',
      lastName: json['lastName'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
      lbsWeight: json['lbsWeight'] ?? '',
      maritalStatus: json['maritalStatus'] ?? '',
      otherConditions: json['otherConditions'] ?? '',
      patientId: json['patientId'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      program: json['program'] ?? '',
      providerName: json['providerName'] ?? '',
      recreationalDrugUse: json['recreationalDrugUse'] ?? '',
      smoking: json['smoking'] ?? '',
      smokingSubstance: json['smokingSubstance'] ?? '',
      thyroidMedication: json['thyroidMedication'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'alcoholConsumption': alcoholConsumption,
      'bloodGroup': bloodGroup,
      'clinic': clinic,
      'created': created,
      'dateOfBirth': dateOfBirth,
      'diabetesMedication': diabetesMedication,
      'diagnosis': diagnosis,
      'email': email,
      'ethnicity': ethnicity,
      'fallsLastYear': fallsLastYear,
      'feet': feet,
      'firstName': firstName,
      'gender': gender,
      'hearingAbility': hearingAbility,
      'hypertensionMedication': hypertensionMedication,
      'inches': inches,
      'lastName': lastName,
      'lastUpdated': lastUpdated,
      'lbsWeight': lbsWeight,
      'maritalStatus': maritalStatus,
      'otherConditions': otherConditions,
      'patientId': patientId,
      'phoneNumber': phoneNumber,
      'program': program,
      'providerName': providerName,
      'recreationalDrugUse': recreationalDrugUse,
      'smoking': smoking,
      'smokingSubstance': smokingSubstance,
      'thyroidMedication': thyroidMedication,
    };
  }
} 