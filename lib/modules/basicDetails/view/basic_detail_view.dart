import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/basicDetails/controller/basic_detail_controller.dart';
import 'package:flutter/services.dart';

import '../../../utils/appConst/app_colors.dart';
import '../../../utils/date_input_fomatter.dart';
import '../../../utils/no_emoji_input_formatter.dart';
import '../../../utils/reusableWidgets/custom_dropdown_field.dart';
import '../../../utils/reusableWidgets/custom_text_field_for_app.dart';
import '../../../utils/reusableWidgets/custom_slider_field.dart';
import '../../../utils/reusableWidgets/reusable_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';

class BasicDetailView extends GetView<BasicDetailController> {
  const BasicDetailView({super.key});
  static const String routeName = "/BasicDetailView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      return SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: context.height * .04),
              _buildSectionTitle("Patient Basic Details", 24),
              SizedBox(height: context.height * .02),
              _buildReadOnlyIndicator(),
              _buildSubjectInformationSection(context),
              const SizedBox(height: 32),
              _buildOtherDetailsSection(context),
              const SizedBox(height: 32),
              _buildActionButtons(context),
              const SizedBox(height: 32),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildSectionTitle(String title, double fontSize) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This form has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value &&
          !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed form. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildSubjectInformationSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _buildSectionTitle("Subject Information", 18),
        const SizedBox(height: 16),
        _buildIdentificationRow(context),
        const SizedBox(height: 24),
        _buildPhysicalAttributesRow(context),
        const SizedBox(height: 24),
        _buildPersonalInfoRow(context),
        const SizedBox(height: 24),
        _buildDemographicsRow(context),
      ],
    );
  }

  Widget _buildIdentificationRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.patientIdController,
              hintText: "Patient ID (optional)",
              title: "Patient ID",
              isRequired: false,
              textInputType: TextInputType.number,
              errorText: controller.patientIdError.value,
              readOnly: controller.isReadOnly.value,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                NoEmojiInputFormatter(),
              ],
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.dobController,
              textInputType: TextInputType.datetime,
              hintText: "Select Date of Birth",
              title: "Date of Birth (mm/dd/yyyy)",
              inputFormatters: [DateInputFormatter()],
              errorText: controller.dobError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                // Reset the flag when user modifies DOB
                controller.isDobFromLogin.value = false;
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.genderController,
              hintText: "Select Gender",
              title: "Gender",
              items: controller.genderOptions,
              errorText: controller.genderError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildPhysicalAttributesRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.heightFeetController,
              hintText: "feet",
              title: "Height (ft)",
              textInputType: TextInputType.number,
              errorText: controller.heightFeetError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.heightInchesController,
              hintText: "inches",
              title: "Height (in)",
              textInputType: TextInputType.number,
              errorText: controller.heightInchesError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.weightController,
              hintText: "Enter Weight in lbs",
              title: "Weight (lbs)",
              maxLength: 3,
              textInputType: TextInputType.number,
              errorText: controller.weightError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildPersonalInfoRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextFieldForApp(
              textEditingController: controller.firstNameController,
              hintText: "First Name",
              title: "First Name",
              errorText: controller.firstNameError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomTextFieldForApp(
              textEditingController: controller.lastNameController,
              hintText: "Last Name",
              title: "Last Name",
              errorText: controller.lastNameError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.maritalStatusController,
              hintText: "Select Marital Status",
              title: "Marital Status",
              items: controller.maritalStatusOptions,
              errorText: controller.maritalStatusError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildDemographicsRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomDropdownField(
              textController: controller.ethnicityController,
              hintText: "Select Ethnicity/Race",
              title: "Ethnicity/Race",
              items: controller.ethnicityOptions,
              errorText: controller.ethnicityError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.bloodGroupController,
              hintText: "Select Blood Group (if known)",
              title: "Blood Group",
              isRequired: true,
              items: controller.bloodGroupOptions,
              errorText: controller.bloodGroupError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.providerNameController,
              hintText: "Select Provider Name",
              title: "Provider Name",
              isRequired: true,
              items: controller.providerNameOptions,
              errorText: controller.providerNameError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildOtherDetailsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Other Details", 18),
        const SizedBox(height: 16),
        _buildDiagnosisRow1(context),
        const SizedBox(height: 16),
        _buildDiagnosisRow2(context),
        const SizedBox(height: 24),
        _buildMedicationRow(context),
        const SizedBox(height: 24),
        _buildLifestyleRow(context),
        const SizedBox(height: 24),
        _buildSmokingRow(context),
        const SizedBox(height: 24),
        _buildHearingAbilityRow(context),
      ],
    );
  }

  Widget _buildDiagnosisRow1(BuildContext context) {
    return Obx(
      () => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownField(
            textController: controller.otherConditionsController,
            hintText: "Select Other Conditions",
            title: "Other Conditions",
            items: controller.otherConditionsOptions,
            isMultiSelect: true,
            errorText: controller.otherConditionsError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              // Check if "Other" is selected
              final selectedIds = value.split(', ');
              final otherId = controller.otherConditionsOptions
                  .firstWhere(
                    (item) => item.name == "Other",
                    orElse: () => controller.otherConditionsOptions.first,
                  )
                  .id;

              controller.isConditionOther.value = selectedIds.contains(otherId);
              controller.validateForm();
            },
          ),
          const SizedBox(width: 16),
          if (controller.isConditionOther.value)
            _buildOtherTextField(
              context,
              controller.conditionOtherController,
              "Specify Condition",
              controller.otherConditionsError,
            ),
          CustomDropdownField(
            textController: controller.diabetesMedicationController,
            hintText: "Select Duration",
            title: "On Diabetes Medication Since",
            items: controller.diabetesDurationOption,
            errorText: controller.diabetesMedicationError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDiagnosisRow2(BuildContext context) {
    return Obx(
      () => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownField(
            textController: controller.diagnosisController,
            hintText: "Select Diagnosis (optional)",
            title: "Diagnosis (ICD-10 Code)",
            items: controller.diagnosisOptions,
            isMultiSelect: true,
            isRequired: false,
            errorText: controller.diagnosisError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              // Check if "Other" is selected
              final selectedIds = value.split(', ');
              final otherId = controller.diagnosisOptions
                  .firstWhere(
                    (item) => item.name == "Other",
                    orElse: () => controller.diagnosisOptions.first,
                  )
                  .id;

              controller.isDiagnosisOther.value = selectedIds.contains(otherId);
              controller.validateForm();
            },
          ),
          const SizedBox(width: 16),
          if (controller.isDiagnosisOther.value)
            _buildOtherTextField(
              context,
              controller.diagnosisOtherController,
              "Specify Diagnosis",
              controller.diagnosisError,
            ),
          CustomDropdownField(
            textController: controller.hypertensionMedicationController,
            hintText: "Select Duration",
            title: "On Hypertension Medication Since",
            items: controller.medicationDurationOptions,
            errorText: controller.hypertensionMedicationError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.validateForm();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomDropdownField(
              textController: controller.thyroidMedicationController,
              hintText: "Select Duration",
              title: "On Thyroid Medication Since",
              items: controller.thyroidDurationOptions,
              errorText: controller.thyroidMedicationError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.fallsLastYearController,
              hintText: "Select Yes/No",
              title: "Falls in the Last One Year",
              items: controller.yesNoOptions,
              errorText: controller.fallsLastYearError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildSmokingRow(BuildContext context) {
    return Obx(
      () => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownField(
            textController: controller.smokingController,
            hintText: "Select Yes/No",
            title: "Smoking",
            items: controller.yesNoOptions,
            errorText: controller.smokingError.value,
            readOnly: controller.isReadOnly.value,
            onChanged: (value) {
              controller.isSmoking.value = value == "1";
              controller.validateForm();
            },
          ),
          const SizedBox(width: 16),
          if (controller.isSmoking.value)
            CustomDropdownField(
              textController: controller.smokingSubstanceController,
              hintText: "Select Substance",
              title: "Smoking Substance",
              items: controller.smokingSubstanceOptions,
              errorText: controller.smokingSubstanceError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            )
          else
            Expanded(child: Container()),

        ],
      ),
    );
  }

  Widget _buildLifestyleRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomDropdownField(
              textController: controller.alcoholConsumptionController,
              hintText: "Select Yes/No",
              title: "Alcohol Consumption",
              items: controller.yesNoOptions,
              errorText: controller.alcoholConsumptionError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            CustomDropdownField(
              textController: controller.recreationalDrugUseController,
              hintText: "Select Yes/No",
              title: "Recreational Drug Use",
              items: controller.yesNoOptions,
              errorText: controller.recreationalDrugUseError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.validateForm();
              },
            ),
          ],
        ));
  }

  Widget _buildHearingAbilityRow(BuildContext context) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomSliderField(
              title: "How would you rate your hearing ability on a scale from 1 to 10?",
              question: null,
              value: controller.hearingAbilityValue.value,
              min: 1.0,
              max: 10.0,
              divisions: 9,
              errorText: controller.hearingAbilityError.value,
              readOnly: controller.isReadOnly.value,
              onChanged: (value) {
                controller.hearingAbilityValue.value = value;
                controller.validateForm();
              },
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()), // Empty space for layout consistency

          ],
        ));
  }

  Widget _buildOtherTextField(BuildContext context,
      TextEditingController controller, String hint, RxString error) {
    return CustomTextFieldForApp(
      textEditingController: controller,
      padding: const EdgeInsets.only(right: 16),
      hintText: hint,
      title: hint,
      errorText: error.value,
      readOnly: this.controller.isReadOnly.value,
      onChanged: (value) => this.controller.validateForm(),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: context.width * .22,
          title: 'Submit',
          onTap: controller.onSubmitClick,
          isLoading: controller.isLoading.value,
        );
      }
    });
  }
}
