import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:exif/exif.dart' as exif;
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/meta/controller/meta_controller.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/modules/microscope/view/wifi_instruction_view.dart';
import 'package:gt_plus/modules/scanImages/controller/camera_manager.dart';
import 'package:gt_plus/modules/scanImages/view/face_mesh_view.dart';
import 'package:gt_plus/modules/scanImages/view/scan_image_capture_screen.dart';
import 'package:gt_plus/modules/scanImages/view/scan_image_preview_screen.dart';
import 'package:gt_plus/modules/scanImages/view/scan_images_main_screen.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import 'package:image/image.dart' as img;
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as path;
import '../../../models/clinic_details_model.dart';
import '../../../utils/dialogs/successfully_uploaded_dialog.dart';
import '../../../utils/reusableWidgets/resusable_snackbar.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';

enum ScanImageType {
  faceMesh(
    title: "Face Mesh",
    imagePath: AppImages.imgFaceMesh,
    isFrontCamera: true,
    fileNamePrefix: "face_mesh",
    routeName: FaceMeshView.routeName,
  ),
  eyeRight(
      title: "Eye Right",
      imagePath: AppImages.imgRightEye,
      isFrontCamera: false,
      fileNamePrefix: "right_eye",
      routeName: ScanImageCaptureScreen.routeName,
      placeholderImagePath: AppImages.imgEyePlaceholder),
  eyeLeft(
      title: "Eye Left",
      imagePath: AppImages.imgLeftEye,
      isFrontCamera: false,
      fileNamePrefix: "left_eye",
      routeName: ScanImageCaptureScreen.routeName,
      placeholderImagePath: AppImages.imgEyePlaceholder),
  tongueImage(
      title: "Tongue",
      imagePath: AppImages.imgTongue,
      isFrontCamera: false,
      fileNamePrefix: "tongue",
      routeName: ScanImageCaptureScreen.routeName,
      placeholderImagePath: AppImages.imgTonguePlaceholder),
  teethImage(
      title: "Teeth",
      imagePath: AppImages.imgTeeth,
      isFrontCamera: false,
      fileNamePrefix: "teeth",
      routeName: ScanImageCaptureScreen.routeName,
      placeholderImagePath: AppImages.imgTeethPlaceholder),
  palmImage(
    title: "Palm",
    imagePath: AppImages.imgPalm,
    isFrontCamera: false,
    fileNamePrefix: "palm",
    routeName: ScanImageCaptureScreen.routeName,
    placeholderImagePath: AppImages.imgPalmPlaceholder,
  ),
  skinImage(
      title: "Skin",
      imagePath: AppImages.imgFace,
      isFrontCamera: false,
      fileNamePrefix: "skin",
      routeName: WifiInstructionView.routeName);

  const ScanImageType({
    required this.title,
    required this.imagePath,
    required this.routeName,
    required this.isFrontCamera,
    required this.fileNamePrefix,
    this.placeholderImagePath,
  });

  final String title;
  final String imagePath;
  final String routeName;
  final String fileNamePrefix;
  final bool isFrontCamera;
  final String? placeholderImagePath;

  String get fieldName {
    return toString().split('.').last;
  }
}

class ScanImagesController extends GetxController {
  ScanImageType? selectedScanImageType;

  //  late InstructionConfig config;
  final RxBool isInitialized = false.obs;
  final RxString imagePath = ''.obs;
  final RxBool isLoading = true.obs;
  final RxBool isDoneButtonLoading = false.obs;
  final RxBool isUsingFrontCamera = true.obs;

  // For tap-to-focus functionality
  final RxBool isFocusing = false.obs;
  final Rx<Offset> focusPoint = Offset.zero.obs;
  final Rx<Offset> relativeFocusPoint = Offset.zero.obs;

  final MetaController _metaController = MetaController();
  RxList<ScanImageDataModel> scanImageDataList = <ScanImageDataModel>[].obs;
  final PrefsService _prefsService = PrefsService();
  final ApiService _apiService = ApiService();
  final FirebaseRemoteConfigService _remoteConfigService =
      FirebaseRemoteConfigService();

  // Camera manager instance
  late CameraManager _cameraManager;
  CameraController? get cameraController => _cameraManager.cameraController;
  CameraManager get cameraManager => _cameraManager;

  @override
  void onInit() {
    super.onInit();
    _cameraManager = CameraManager(remoteConfigService: _remoteConfigService);
  }

  @override
  void onReady() {
    initModel(Get.context!, true);
    super.onReady();
  }

  @override
  void onClose() {
    if (_cameraManager.cameraController != null) {
      _cameraManager.dispose();
      debugPrint('Camera disposed in onClose');
    }
    super.onClose();
  }

  Future<void> initModel(BuildContext context, bool setLoading) async {
    try {
      if (setLoading) {
        isLoading.value = true;
      }
      await _metaController.getMetaData();
      scanImageDataList.value = ScanImageType.values
          .map((type) => ScanImageDataModel(
                title: type.title,
                image: type.imagePath,
                isCompleted: _metaController.isScanImageCompleted(type),
                onTap: () => onItemTap(type, context),
              ))
          .toList();
      scanImageDataList.refresh();
      update();
    } catch (e) {
      reusableSnackBar(message: "Something went wrong");
    } finally {
      if (setLoading) {
        isLoading.value = false;
      }
    }
  }

  void onDoneClick() {
    _cameraManager.dispose();
    Get.offAllNamed(MetaView.routeName);
  }

  Future<bool> _isDemoMode() async {
    return await _prefsService.getDemoMode();
  }

  Future<void> switchCamera(BuildContext context) async {
    try {
      isLoading.value = true;
      isUsingFrontCamera.value = !isUsingFrontCamera.value;

      await _cameraManager.initializeCamera(
        isFrontCamera: isUsingFrontCamera.value,
        addListenerCallback: addCameraListener,
        context: context,
      );

      isInitialized.value = true;
      update();
    } catch (e) {
      debugPrint('Error switching camera: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void addCameraListener(BuildContext context) {
    _cameraManager.cameraController!.addListener(() {
      if (_cameraManager.cameraController!.value.isInitialized) {
        final newOrientation = _cameraManager.getDeviceOrientation(context);
        if (newOrientation !=
            _cameraManager.cameraController!.value.lockedCaptureOrientation) {
          _cameraManager.cameraController!
              .lockCaptureOrientation(newOrientation);
        }
      }
    });
  }

  Future<void> initializeCamera(BuildContext context) async {
    try {
      isLoading.value = true;
      isUsingFrontCamera.value = selectedScanImageType?.isFrontCamera ?? true;

      await _cameraManager.initializeCamera(
        isFrontCamera: isUsingFrontCamera.value,
        addListenerCallback: addCameraListener,
        context: context,
      );

      isInitialized.value = true;
      update();
    } catch (e) {
      debugPrint('Error initializing camera: $e');
      isInitialized.value = false;
      reusableSnackBar(
          message: "Failed to initialize camera. Please try again.");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> takePicture(BuildContext context) async {
    if (_cameraManager.cameraController == null ||
        !_cameraManager.cameraController!.value.isInitialized) {
      return;
    }

    try {
      isLoading.value = true;
      // Take picture using camera manager
      final XFile? photo = await _cameraManager.takePicture();
      if (photo == null) {
        throw Exception('Failed to capture image');
      }

      final String originalPath = photo.path;
      imagePath.value = "";

      final imageFile = File(originalPath);
      final bytes = await imageFile.readAsBytes();
      img.Image? originalImage = img.decodeImage(bytes);

      if (originalImage == null) {
        throw Exception('Failed to decode image');
      }

      final exifData = await exif.readExifFromBytes(bytes);
      final orientation = exifData['Image Orientation']?.printable;

      if (orientation != null) {
        originalImage = applyExifOrientation(originalImage, orientation);
      }

      final imageWidth = originalImage.width;
      final imageHeight = originalImage.height;

      bool isImagePortrait = imageHeight > imageWidth;
      double scanAreaPercentage = 0.75;
      double cropSize =
          (isImagePortrait ? imageWidth : imageHeight) * scanAreaPercentage;

      // Ensure valid crop size
      cropSize = cropSize
          .clamp(0.0,
              isImagePortrait ? imageWidth.toDouble() : imageHeight.toDouble())
          .roundToDouble();

      int cropLeft = ((imageWidth - cropSize) / 2).round();
      int cropTop = ((imageHeight - cropSize) / 2).round();

      // Ensure valid crop area
      if (cropLeft < 0 ||
          cropTop < 0 ||
          (cropLeft + cropSize) > imageWidth ||
          (cropTop + cropSize) > imageHeight) {
        throw Exception('Invalid crop dimensions');
      }

      final croppedImage = img.copyCrop(
        originalImage,
        x: cropLeft,
        y: cropTop,
        width: cropSize.round(),
        height: cropSize.round(),
      );

      final tempDir = await getTemporaryDirectory();
      final tempPath =
          '${tempDir.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png';
      final croppedFile = File(tempPath);
      await croppedFile.writeAsBytes(img.encodePng(croppedImage));

      // Important: Set camera controller to null before navigation to prevent access to disposed controller
      _cameraManager.dispose();

      // Then navigate
      imagePath.value = tempPath;
      Get.toNamed(ScanImagePreviewScreen.routeName);
    } catch (e) {
      debugPrint('Camera Error: $e');
      Get.snackbar(
          'Capture Failed', 'Error: ${e.toString().replaceAll('\n', ' ')}');
    } finally {
      isLoading.value = false;
    }
  }

  img.Image applyExifOrientation(img.Image image, String orientation) {
    switch (orientation) {
      case '1': // Normal
        return image;
      case '2': // Mirror horizontal
        return img.flipHorizontal(image);
      case '3': // Rotate 180
        return img.copyRotate(image, angle: 180);
      case '4': // Mirror vertical
        return img.flipVertical(image);
      case '5': // Mirror horizontal and rotate 270 CW
        return img.flipHorizontal(img.copyRotate(image, angle: -90));
      case '6': // Rotate 90 CW
        return img.copyRotate(image, angle: -90);
      case '7': // Mirror horizontal and rotate 90 CW
        return img.flipHorizontal(img.copyRotate(image, angle: 90));
      case '8': // Rotate 270 CW
        return img.copyRotate(image, angle: 90);
      default:
        return image;
    }
  }

  Future<String> getFormattedFileName({
    required String suffix,
    required String extension,
  }) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final platform = Theme.of(Get.context!).platform == TargetPlatform.iOS
        ? 'ios'
        : 'android';
    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = 'v${packageInfo.version}';
    return '${timestamp}_${platform}_${appVersion}_$suffix.$extension';
  }

  Future<void> uploadPhoto(BuildContext context) async {
    try {
      isDoneButtonLoading.value = true;
      final isDemoMode = await _isDemoMode();
      if (isDemoMode) {
        log("Demo mode detected. Skipping actual GCS upload.");
        successfullyUploadedDialog(
          context: context,
          message: "Image has been successfully uploaded",
          onTap: () async {
            ReusableDialog.close();
            Get.until(
                (route) => route.settings.name == ScanImagesMainView.routeName);
          },
        );
        return;
      }

      if (imagePath.value.isEmpty) {
        Get.snackbar('Error', 'No image to upload');
        return;
      }

      final imageFile = File(imagePath.value);
      if (!await imageFile.exists()) {
        Get.snackbar('Error', 'Image file not found');
        return;
      }

      final fileSize = await imageFile.length();
      if (fileSize == 0) {
        Get.snackbar('Error', 'Empty image file');
        return;
      }

      final extension = path.extension(imagePath.value).replaceFirst('.', '');
      final prefix = selectedScanImageType?.fileNamePrefix ?? "";
      String fileName = await getFormattedFileName(
        suffix: prefix,
        extension: extension,
      );

      ClinicDetailsModel? clinicDetailsModel =
          await _prefsService.getClinicDetails();

      String bucketName = (clinicDetailsModel?.clinicName ?? "").toLowerCase();
      String identifier = await _prefsService.getIdentifier();
      final gcsPath = '$identifier/$fileName';

      // Prepare Google Cloud Storage upload
      final credentials = await loadServiceAccountCredentials();
      final httpClient = await clientViaServiceAccount(
        credentials,
        [StorageApi.devstorageFullControlScope],
      );

      final storage = StorageApi(httpClient);
      final media = Media(
        imageFile.openRead(),
        fileSize,
        contentType: _getContentType(extension),
      );

      // Execute upload
      final response = await storage.objects.insert(
        Object(name: gcsPath),
        bucketName,
        uploadMedia: media,
      );

      if (response.id == null) throw Exception('Upload failed');
      log("GCP : path:$gcsPath \nbucketName: $bucketName");
      await postImageNetworkCall(context, "$bucketName/$gcsPath");
    } on DetailedApiRequestError catch (e) {
      debugPrint("error is  : ${e.message}");
      Get.snackbar('Upload Error', 'Failed to upload image');
    } catch (e) {
      debugPrint("error is  : $e");
      Get.snackbar('Upload Error', 'Failed to upload image');
    } finally {
      isDoneButtonLoading.value = false;
    }
  }

  Future<void> postImageNetworkCall(BuildContext context, String path) async {
    try {
      isDoneButtonLoading.value = true;
      bool response = await _apiService.postImage(
        type: (selectedScanImageType?.title ?? "").replaceAll(' ', ''),
        path: path,
      );
      if (response) {
        await initModel(context, false);
        successfullyUploadedDialog(
          context: context,
          message: "Image has been successfully uploaded",
          onTap: () async {
            // Explicitly dispose camera before navigation

            _cameraManager.dispose();
            ReusableDialog.close();
            Get.until(
                (route) => route.settings.name == ScanImagesMainView.routeName);
          },
        );
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isDoneButtonLoading.value = false;
    }
  }

  String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'image/jpeg';
    }
  }

  Future<ServiceAccountCredentials> loadServiceAccountCredentials() async {
    String jsonContent =
        await rootBundle.loadString('assets/gCloud/credentials.json');
    final Map<String, dynamic> jsonMap = jsonDecode(jsonContent);
    return ServiceAccountCredentials.fromJson(jsonMap);
  }

  Future<void> setFocusDirectly(Offset normalizedPosition) async {
    if (isLoading.value) return;

    // Store the original coordinates for the focus indicator UI
    relativeFocusPoint.value = normalizedPosition;

    // Use the normalized coordinates for actual camera focus
    await _cameraManager.setFocusPoint(
      normalizedPosition,
      (focusing) => isFocusing.value = focusing,
    );
  }

  /// Reinitializes the camera when returning from preview to retake a photo
  Future<void> reinitializeCamera(BuildContext context) async {
    try {
      isLoading.value = true;
      // If camera was disposed, we need to initialize it again
      if (_cameraManager.cameraController == null ||
          !_cameraManager.cameraController!.value.isInitialized) {
        await _cameraManager.initializeCamera(
          isFrontCamera: isUsingFrontCamera.value,
          addListenerCallback: addCameraListener,
          context: context,
        );
        isInitialized.value = true;
      }
    } catch (e) {
      debugPrint('Error reinitializing camera: $e');
      reusableSnackBar(
          message: "Failed to initialize camera. Please try again.");
    } finally {
      isLoading.value = false;
    }
  }

  void onItemTap(ScanImageType type, BuildContext context) {
    // Dispose any existing camera controller first
    if (_cameraManager.cameraController != null) {
      _cameraManager.dispose();
    }

    selectedScanImageType = type;
    imagePath.value = '';
    initializeCamera(context);
    Get.toNamed(type.routeName);
  }
}

class ScanImageDataModel {
  const ScanImageDataModel({
    required this.title,
    required this.image,
    required this.isCompleted,
    required this.onTap,
  });

  final String title;
  final String image;
  final bool isCompleted;
  final void Function() onTap;
}
