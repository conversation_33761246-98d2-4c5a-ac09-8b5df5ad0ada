import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:camera/camera.dart';

import 'package:gt_plus/modules/scanImages/controller/scan_images_controller.dart';
import '../../../utils/appConst/app_colors.dart';
import '../../../utils/helpers/image_name_helper.dart';
import 'camera_overlay.dart';

class ScanImageCaptureScreen extends GetView<ScanImagesController> {
  const ScanImageCaptureScreen({super.key});

  static const routeName = "/ScanImageCaptureScreen";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(ImageNameHelper.getCapitalizedImageName(controller.selectedScanImageType?.title ?? "")),
      ),
      body: SafeArea(child: Obx(() {
        return controller.isLoading.value
            ? const Center(
                child: CircularProgressIndicator(
                  color: AppColors.orange,
                ),
              )
            : !controller.isInitialized.value
                ? const Center(
                    child: Text(
                      "Camera initializing...",
                      style: TextStyle(color: Colors.white),
                    ),
                  )
                : _buildLandscapeLayout(context);
      })),
    );
  }



  Widget _buildLandscapeLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildCameraPreview(),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCaptureButton(context),
            //  const SizedBox(height: 30),
          //    _buildSwitchCameraButton(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCameraPreview() {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (controller.cameraController == null ||
            !controller.cameraController!.value.isInitialized) {
          return const Center(
            child: Text(
              "Camera not available",
              style: TextStyle(color: Colors.white),
            ),
          );
        }

        final aspectRatio = controller.cameraController!.value.aspectRatio;
        final maxWidth = constraints.maxWidth;
        final maxHeight = constraints.maxHeight;
        double previewWidth, previewHeight;
        if (maxWidth / maxHeight > aspectRatio) {
          previewHeight = maxHeight;
          previewWidth = previewHeight * aspectRatio;
        } else {
          previewWidth = maxWidth;
          previewHeight = maxWidth / aspectRatio;
        }
        final previewOffsetX = (maxWidth - previewWidth) / 2;
        final previewOffsetY = (maxHeight - previewHeight) / 2;

        final previewRect = Rect.fromLTWH(
          previewOffsetX,
          previewOffsetY,
          previewWidth,
          previewHeight,
        );

        return Stack(
          fit: StackFit.expand,
          children: [
            // Black background
            Container(color: Colors.black),

            // Camera preview
            Center(
              child: SizedBox(
                width: previewWidth,
                height: previewHeight,
                child: Builder(
                  builder: (context) {
                    try {
                      return CameraPreview(controller.cameraController!);
                    } catch (e) {
                      // Handle exception when camera is disposed
                      debugPrint('Camera error: $e');
                      return const Center(
                        child: Text(
                          "Camera disconnected",
                          style: TextStyle(color: Colors.white),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),

            Center(
              child: SizedBox(
                width: previewWidth,
                height: previewHeight,
                child: CameraOverlay(
                  previewWidth: previewWidth,
                  previewHeight: previewHeight,
                  overlayColour: const Color.fromRGBO(0, 0, 0, 0.7),
                ),
              ),
            ),

            // Placeholder image overlay - fit inside CameraOverlay scan area
            if (controller.selectedScanImageType?.placeholderImagePath != null)
              Center(
                child: SizedBox(
                  width: previewWidth,
                  height: previewHeight,
                  child: Center(
                    child: SizedBox(
                      width: (previewHeight > previewWidth ? previewWidth : previewHeight) * 0.75,
                      height: (previewHeight > previewWidth ? previewWidth : previewHeight) * 0.75,
                      child: Image.asset(
                        controller.selectedScanImageType!.placeholderImagePath!,
                        fit: BoxFit.contain,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                ),
              ),

            Obx(() {
              if (controller.isFocusing.value) {
                // Calculate exact focus point position
                final focusPoint = controller.relativeFocusPoint.value;

                // Map normalized 0-1 coordinates to actual preview pixels
                final focusX = previewOffsetX + (focusPoint.dx * previewWidth);
                final focusY = previewOffsetY + (focusPoint.dy * previewHeight);

                return Positioned(
                  // Center the 50x50 focus indicator on the tap point
                  left: focusX - 25,
                  top: focusY - 25,
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.yellow, width: 2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Container(
                        height: 10,
                        width: 10,
                        decoration: BoxDecoration(
                          color: Colors.yellow.withValues(alpha: 0.5),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                );
              } else {
                return const SizedBox.shrink();
              }
            }),

            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTapDown: (TapDownDetails details) {
                  if (previewRect.contains(details.localPosition)) {
                    // Convert absolute tap position to position relative to the preview
                    final relativeX = details.localPosition.dx - previewOffsetX;
                    final relativeY = details.localPosition.dy - previewOffsetY;

                    // Calculate normalized position (0-1) for the camera focus
                    final normalizedX = relativeX / previewWidth;
                    final normalizedY = relativeY / previewHeight;

                    debugPrint(
                        "Tap at normalized position: ($normalizedX, $normalizedY)");

                    // Send the normalized coordinates to the controller
                    controller
                        .setFocusDirectly(Offset(normalizedX, normalizedY));
                  }
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCaptureButton(BuildContext context) {
    return Obx(() => GestureDetector(
          onTap: controller.isLoading.value
              ? null
              : () => controller.takePicture(context),
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: controller.isLoading.value ? Colors.grey : Colors.white,
                width: 3,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: controller.isLoading.value ? Colors.grey : Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ));
  }
}
