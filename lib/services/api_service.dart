import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:gt_plus/models/clinic_details_model.dart';
import 'package:gt_plus/models/login_model.dart';
import 'package:gt_plus/models/meta_data_model.dart';
import 'package:gt_plus/models/provider_list_model.dart';
import 'package:gt_plus/models/refresh_token_model.dart';
import 'package:gt_plus/services/mock_api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/utils/appConst/app_urls.dart';

import 'http_service.dart';

enum BasicDetailTypes {
  Audiometry("Audiometry"),
  BasicDetails("BasicDetails"),
  Cognivue("Cognivue"),
  SpeechInNoise("QuickSin"),
  ExtraData("ExtraData");

  final String value;
  const BasicDetailTypes(this.value);
}

class ApiService {
  final HttpService _httpService = HttpService();
  final PrefsService _prefsService = PrefsService();

  // Add authorization headers
  Future<Map<String, String>> _getAuthHeaders() async {
    String token = await _prefsService.getAccessToken();
    return {
      ...HttpService.defaultHeaders,
      "Authorization": "Bearer $token",
    };
  }

  Future<bool> _isDemoMode() async {
    return await _prefsService.getDemoMode();
  }

  // 1. Clinic Details API
  Future<ClinicDetailsModel?> getClinicDetails(String clinicName) async {
    try {
      // If in demo mode, return mock data
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Returning mock clinic details");
        return MockApiService.getMockClinicDetailsModel();
      }

      final body = {"clinicName": clinicName};
      final response =
          await _httpService.post(ApiUrls.postClinicDetails, body: body);

      return HttpService.handleResponse<ClinicDetailsModel>(
        response,
        (data) => ClinicDetailsModel.fromJson(data),
      );
    } catch (e) {
      debugPrint("Error during getClinicDetails API call: $e");
      return null;
    }
  }

  // 2. Login API
  Future<LoginModel?> login(String phoneNumber, String clinicName,
      {String? email, Map<String, dynamic>? additionalData}) async {
    try {
      // If in demo mode, return mock login data
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Returning mock login data");
        return MockApiService.getMockLoginModel();
      }

      final Map<String, dynamic> body = {};

      // Add phone number if provided
      if (phoneNumber.isNotEmpty) {
        body["phoneNumber"] = phoneNumber;
      }

      if (clinicName.isNotEmpty) {
        body["clinicName"] = clinicName;
      }

      // Add email if provided
      if (email != null) {
        body["email"] = email;
      }

      // Add any additional data if provided
      if (additionalData != null) {
        body.addAll(additionalData);
      }

      final response = await _httpService.post(ApiUrls.postLogin, body: body);

      return HttpService.handleResponse<LoginModel?>(
          response, (data) => LoginModel.fromJson(data));
    } catch (e) {
      debugPrint("Error during login API call: $e");
      return null;
    }
  }

  // 4. PPG API
  Future<bool> postPpg(
      {required int spo2, required int heartRate, required String path}) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postPpg API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "url": "https://storage.cloud.google.com/$path",
        "spo2": spo2,
        "heartRate": heartRate,
      };

      final response = await _httpService.post(ApiUrls.postPpg,
          headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "PPG Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postPpg,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postPpg API call: $e");
      return false;
    }
  }

  // 5. Temperature API
  Future<bool> postTemperature(
      {required double tempBte, required double tempFh}) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postTemperature API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "tempBte": tempBte,
        "tempFh": tempFh,
      };

      final response = await _httpService.post(ApiUrls.postTemperature,
          headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "Temperature Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postTemperature,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postTemperature API call: $e");
      return false;
    }
  }

  // 6. BP API
  Future<bool> postBp({required int systolic, required int diastolic}) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postBp API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "systolic": systolic,
        "diastolic": diastolic,
      };

      final response =
          await _httpService.post(ApiUrls.postBp, headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "BP Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postBp,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postBp API call: $e");
      return false;
    }
  }

  // 7. Image API
  Future<bool> postImage({required String type, required String path}) async {
    try {
      // Check if in demo mode
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postImage API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "type": type,
        "url": "https://storage.cloud.google.com/$path",
      };

      final response = await _httpService.post(ApiUrls.postImage,
          headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "Image Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postImage,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postImage API call: $e");
      return false;
    }
  }

  // 7. Image API (FaceMeshUrl)
  Future<bool> postFaceMeshUrl({
    required String type,
    required String faceDataPath,
    required String geometryPath,
  }) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postFaceMeshUrl API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "type": type,
        "faceDataUrl": "https://storage.cloud.google.com/$faceDataPath",
        "geometryUrl": "https://storage.cloud.google.com/$geometryPath",
      };

      final response = await _httpService.post(ApiUrls.postImage,
          headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "Image Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postImage,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postFaceMeshUrl API call: $e");
      return false;
    }
  }

  // 8. Details API
  Future<bool> postDetails(
      {required BasicDetailTypes type,
      required Map<String, dynamic> detailsData}) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postDetails API call");
        return true;
      }

      final headers = await _getAuthHeaders();
      final body = {
        "type": type.value,
        "data": detailsData,
      };

      final response = await _httpService.post(ApiUrls.postDetails,
          headers: headers, body: body);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "Details Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postDetails,
                  headers: refreshedHeaders, body: body);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postDetails API call: $e");
      return false;
    }
  }

  // 9. Face Mesh API
  Future<bool> postFaceMesh(Map<String, dynamic> faceMeshData) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postFaceMesh API call");
        return true;
      }

      final headers = await _getAuthHeaders();

      final response = await _httpService.post(ApiUrls.postFaceMesh,
          headers: headers, body: faceMeshData);

      return await HttpService.handleResponseWithRefresh<bool>(
            response,
            (data) => data["message"] == "Face Mesh Data Saved Successfully",
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.post(ApiUrls.postFaceMesh,
                  headers: refreshedHeaders, body: faceMeshData);
            },
          ) ??
          false;
    } catch (e) {
      debugPrint("Error during postFaceMesh API call: $e");
      return false;
    }
  }

  // 10. Providers API
  Future<ProviderListModel?> getProviders(
      {required String clinicName, required String programName}) async {
    try {
      // If in demo mode, return mock data
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Returning mock providers data");
        return MockApiService.getMockProviderListModel();
      }

      final headers = await _getAuthHeaders();
      final params = {
        "clinicName": clinicName,
        "programName": programName,
      };

      final response = await _httpService.get(ApiUrls.getProviders,
          headers: headers, params: params);

      return await HttpService.handleResponseWithRefresh<ProviderListModel?>(
        response,
        (data) => ProviderListModel.fromJson(data),
        () async {
          final refreshedHeaders = await _getAuthHeaders();
          return await _httpService.get(ApiUrls.getProviders,
              headers: refreshedHeaders, params: params);
        },
      );
    } catch (e) {
      debugPrint("Error during getProviders API call: $e");
      return null;
    }
  }

  // 11. Meta Data API
  Future<MetaDataModel?> getMetaData() async {
    try {
      // If in demo mode, return mock data
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Returning mock meta data");
        return MockApiService.getMockMetaDataModel();
      }

      final headers = await _getAuthHeaders();

      // First attempt
      try {
        final response = await _httpService.get(
          ApiUrls.getMetaData,
          headers: headers,
        );

        return await HttpService.handleResponseWithRefresh<MetaDataModel?>(
          response,
          (data) => MetaDataModel.fromJson(data),
          () async {
            final refreshedHeaders = await _getAuthHeaders();
            return await _httpService.get(
              ApiUrls.getMetaData,
              headers: refreshedHeaders,
            );
          },
        );
      } catch (e) {
        // If the first attempt failed with a timeout exception
        if (e is TimeoutException) {
          debugPrint("Meta API timeout occurred. Retrying once...");

          // Retry once after a timeout
          final response = await _httpService.get(
            ApiUrls.getMetaData,
            headers: headers,
          );

          return await HttpService.handleResponseWithRefresh<MetaDataModel?>(
            response,
            (data) => MetaDataModel.fromJson(data),
            () async {
              final refreshedHeaders = await _getAuthHeaders();
              return await _httpService.get(
                ApiUrls.getMetaData,
                headers: refreshedHeaders,
              );
            },
          );
        } else {
          // For other exceptions, return null directly
          debugPrint("Error during getMetaData API call: $e");
          return null;
        }
      }
    } catch (e) {
      debugPrint("Error during getMetaData API call: $e");
      return null;
    }
  }

  // 12. Get Details API
  Future<Map<String, dynamic>?> getDetails(
      {required BasicDetailTypes type}) async {
    try {
      // If in demo mode, return mock data
      if (await _isDemoMode()) {
        return null;
      }

      final headers = await _getAuthHeaders();
      final params = {
        "type": type.value,
      };

      final response = await _httpService.get(
        ApiUrls.getDetails,
        headers: headers,
        params: params,
      );

      return await HttpService.handleResponseWithRefresh<Map<String, dynamic>?>(
        response,
        (data) => data,
        () async {
          final refreshedHeaders = await _getAuthHeaders();
          return await _httpService.get(
            ApiUrls.getDetails,
            headers: refreshedHeaders,
            params: params,
          );
        },
      );
    } catch (e) {
      debugPrint("Error during getDetails API call: $e");
      return null;
    }
  }

  Future<Map<String, dynamic>?> postMetaCompletion(
      {required String buttonTitle}) async {
    try {
      if (await _isDemoMode()) {
        debugPrint("Demo mode active: Skipping postMetaCompletion API call");
        return {"success": true, "message": "Demo mode completion successful"};
      }

      final headers = await _getAuthHeaders();

      final body = {
        "completedFlag": buttonTitle,
      };

      final response = await _httpService.post(
        ApiUrls.getComplete,
        headers: headers,
        body: body,
      );
      return await HttpService.handleResponseWithRefresh<Map<String, dynamic>?>(
        response,
        (data) => data,
        () async {
          final refreshedHeaders = await _getAuthHeaders();
          return await _httpService.post(
            ApiUrls.getComplete,
            headers: refreshedHeaders,
            body: body,
          );
        },
      );
    } catch (e) {
      debugPrint("Error during postMetaCompletion API call: $e");
      return null;
    }
  }

  // 9. Technical Support API
  Future<Map<String, dynamic>?> postTechnicalSupport({
    required String issueTitle,
    required String issueDescription,
    required String clinicName,
    required List<String> kitName,
    required String programName,
  }) async {
    try {
      final headers = await _getAuthHeaders();
      final body = {
        "issueTitle": issueTitle,
        "issueDescription": issueDescription,
        "clinicName": clinicName,
        "kitName": kitName,
        "programName": programName,
      };

      final response = await _httpService.post(
        ApiUrls.postSupport,
        headers: headers,
        body: body,
      );

      return await HttpService.handleResponseWithRefresh<Map<String, dynamic>?>(
        response,
        (data) => data,
        () async {
          final refreshedHeaders = await _getAuthHeaders();
          return await _httpService.post(
            ApiUrls.postSupport,
            headers: refreshedHeaders,
            body: body,
          );
        },
      );
    } catch (e) {
      debugPrint("Error during postTechnicalSupport API call: $e");
      return null;
    }
  }
}
