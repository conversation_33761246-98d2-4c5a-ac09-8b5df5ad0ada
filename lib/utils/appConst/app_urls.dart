const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "dev") == "prod";

enum ApiUrls {
  // GT Backend APIs
  postClinicDetails("gt/clinicDetails"),
  postLogin("gt/login"),
  getRefreshToken("gt/refresh-token"),
  getMetaData("gt/metaData"),
  postPpg("gt/ppg"),
  postTemperature("gt/temperature"),
  postBp("gt/bp"),
  postImage("gt/image"),
  postDetails("gt/details"),
  getDetails("gt/details"),
  postFaceMesh("gt/face-mesh"),
  getProviders("gt/providers"),
  getComplete("gt/complete"),
  postSupport("gt/support");

  const ApiUrls(this._url);

  final String _url;

  String getUrl() {
    String baseUrl = isProdEnv
        ? "https://sh-cron-trigger-391397853270.us-west1.run.app/"
        :   // "http://192.168.0.119:5000/";
    "https://sh-cron-trigger-staging-391397853270.us-west1.run.app/";
    return baseUrl + _url;
  }
}
